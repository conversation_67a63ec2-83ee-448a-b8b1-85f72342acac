#include <iostream>
#include "atom/type/ryaml.hpp"

int main() {
    using namespace atom::type;
    
    // Test block array
    std::string block_arr_yaml = R"(
- item1
- 456
- true
    )";

    std::cout << "YAML string: [" << block_arr_yaml << "]" << std::endl;
    std::cout << "Length: " << block_arr_yaml.length() << std::endl;
    std::cout << "First char: '" << (block_arr_yaml.empty() ? ' ' : block_arr_yaml[0]) << "' (ASCII: " << (int)(block_arr_yaml.empty() ? 0 : block_arr_yaml[0]) << ")" << std::endl;

    try {
        YamlParseOptions options;
        YamlValue result = YamlParser::parse(block_arr_yaml, options);

        std::cout << "Parsed successfully!" << std::endl;
        std::cout << "Is array: " << result.is_array() << std::endl;
        std::cout << "Is object: " << result.is_object() << std::endl;
        std::cout << "Is string: " << result.is_string() << std::endl;
        std::cout << "Is null: " << result.is_null() << std::endl;

        if (result.is_array()) {
            std::cout << "Array size: " << result.size() << std::endl;
        } else if (result.is_object()) {
            std::cout << "Object size: " << result.size() << std::endl;
        } else if (result.is_string()) {
            std::cout << "String value: [" << result.as_string() << "]" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cout << "Error: " << e.what() << std::endl;
    }
    
    return 0;
}
